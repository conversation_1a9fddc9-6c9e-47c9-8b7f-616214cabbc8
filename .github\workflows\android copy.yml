name: Build Android 2

on:
  workflow_dispatch:
  push:
    branches:
      - "xD"

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        config:

        - name: Android32
          os: ubuntu-latest
          target: Android32

    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}

    steps:
      - uses: actions/checkout@v4

      - name: Build the mod
        uses: geode-sdk/build-geode-mod@main
        with:
          bindings: geode-sdk/bindings
          build-config: Release
          bindings-ref: main
          combine: true
          target: ${{ matrix.config.target }}

  package:
    name: Package builds
    runs-on: ubuntu-latest
    needs: ['build']

    steps:
      - uses: geode-sdk/build-geode-mod/combine@main
        id: build

      - uses: actions/upload-artifact@v4
        with:
          name: Build Output
          path: ${{ steps.build.outputs.build-output }}
