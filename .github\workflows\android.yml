name: Build Android

on:
  workflow_dispatch:
  push:
    branches:
      - "d"

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        config:
        - name: Android32
          os: ubuntu-latest
          target: Android32

    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}

    steps:
      - uses: actions/checkout@v4

      - name: Build the mod
        uses: geode-sdk/build-geode-mod@main
        with:
          bindings: geode-sdk/bindings
          bindings-ref: main
          combine: false
          target: ${{ matrix.config.target }}

  package:
    name: Package builds
    runs-on: ubuntu-latest
    needs: ['build']

    steps:
      - uses: geode-sdk/build-geode-mod/combine@main
        id: build

      - uses: actions/upload-artifact@v4
        with:
          name: Build Output
          path: ${{ steps.build.outputs.build-output }}
