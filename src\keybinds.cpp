
#include "includes.hpp"
#include "ui/record_layer.hpp"
#include "ui/game_ui.hpp"
#include "ui/clickbot_layer.hpp"
#include "ui/macro_editor.hpp"
#include "ui/render_settings_layer.hpp"
#include "hacks/layout_mode.hpp"
#include "hacks/show_trajectory.hpp"

#include <Geode/modify/CCKeyboardDispatcher.hpp>
#include <Geode/modify/CCTouchDispatcher.hpp>

#ifdef GEODE_IS_WINDOWS

#include <geode.custom-keybinds/include/Keybinds.hpp>
#include <regex>

#endif

const std::vector<std::string> keybindIDs = {
    "open_menu", // "toggle_recording", "toggle_playing", "reset_macro_system" - deshabilitados
    "toggle_speedhack", "toggle_frame_stepper", "step_frame",
    "toggle_render", "toggle_noclip", "show_trajectory"
};

class $modify(CCKeyboardDispatcher) {
  bool dispatchKeyboardMSG(enumKeyCodes key, bool isKeyDown, bool isKeyRepeat) {
  
    auto& g = Global::get();

    int keyInt = static_cast<int>(key);
    if (g.allKeybinds.contains(keyInt) && !isKeyRepeat) {
      for (size_t i = 0; i < 6; i++) {
        if (std::find(g.keybinds[i].begin(), g.keybinds[i].end(), keyInt) != g.keybinds[i].end())
          g.heldButtons[i] = isKeyDown;
      }
    }

    // Interceptar tecla T directamente para grabar sin abrir menú (funciona en modo normal y práctica)
    if (key == KEY_T && isKeyDown && !isKeyRepeat && PlayLayer::get()) {
        // Activar/desactivar grabación directamente sin abrir menú
        if (g.state == state::playing) {
            g.state = state::none;
            // Liberar botones si se estaba reproduciendo
            PlayLayer* pl = PlayLayer::get();
            if (pl) {
                pl->m_player1->releaseAllButtons();
                pl->m_player2->releaseAllButtons();
            }
        }

        g.state = g.state == state::recording ? state::none : state::recording;

        if (g.state == state::recording) {
            g.currentAction = 0;
            g.currentFrameFix = 0;
            g.restart = true;

            PlayLayer* pl = PlayLayer::get();
            if (pl) {
                if (!pl->m_isPaused)
                    pl->pauseGame(false);
            }
        }

        Interface::updateLabels();
        Interface::updateButtons();
        Macro::updateTPS();
        g.lastAutoSaveMS = std::chrono::steady_clock::now();

        // Retornar false para evitar que se procese más y abra el menú
        return false;
    }

    // Interceptar tecla Y directamente para reproducir sin abrir menú
    if (key == KEY_Y && isKeyDown && !isKeyRepeat && PlayLayer::get()) {
        // Activar/desactivar reproducción directamente sin abrir menú
        if (g.state == state::recording) {
            g.state = state::none;
        }

        bool wasPlaying = (g.state == state::playing);
        g.state = g.state == state::playing ? state::none : state::playing;

        // Si se está desactivando la reproducción, liberar todos los botones
        if (wasPlaying && g.state == state::none) {
            PlayLayer* pl = PlayLayer::get();
            if (pl) {
                pl->m_player1->releaseAllButtons();
                pl->m_player2->releaseAllButtons();
            }
        }

        if (g.state == state::playing) {
            g.macro.xdBotMacro = g.macro.botInfo.name == "Paimon Scaler Mod";

            PlayLayer* pl = PlayLayer::get();

            if (pl) {
                // Sincronizar con el frame actual del nivel
                int currentFrame = Global::getCurrentFrame();

                // Encontrar la acción correspondiente al frame actual
                g.currentAction = 0;
                for (size_t i = 0; i < g.macro.inputs.size(); i++) {
                    if (g.macro.inputs[i].frame <= currentFrame) {
                        g.currentAction = i + 1;
                    } else {
                        break;
                    }
                }

                g.currentFrameFix = 0;

                // No reiniciar el nivel - continuar desde la posición actual
                // Solo pausar si no está pausado
                if (!pl->m_isPaused && !pl->m_levelEndAnimationStarted) {
                    pl->pauseGame(false);
                }
            }
        }

        Interface::updateLabels();
        Interface::updateButtons();
        Macro::updateTPS();

        // Retornar false para evitar que se procese más y abra el menú
        return false;
    }

    // Interceptar tecla Q para reiniciar completamente el sistema de macros
    if (key == KEY_Q && isKeyDown && !isKeyRepeat) {

        // Reiniciar completamente el sistema de macros
        g.state = state::none;
        g.currentAction = 0;
        g.currentFrameFix = 0;
        g.restart = false;
        g.firstAttempt = true;
        g.safeMode = false;

        // Limpiar macro actual
        g.macro.inputs.clear();
        g.macro.frameFixes.clear();
        g.macro.author = "";
        g.macro.description = "";
        g.macro.duration = 0.0f;
        g.macro.coins = 0;
        g.macro.seed = 0;

        // Limpiar checkpoints
        g.checkpoints.clear();

        // Reiniciar configuraciones de sesión
        g.currentSession = 0;
        g.lastAutoSaveMS = std::chrono::steady_clock::now();

        // Liberar todos los botones si hay un PlayLayer activo
        PlayLayer* pl = PlayLayer::get();
        if (pl) {
            pl->m_player1->releaseAllButtons();
            pl->m_player2->releaseAllButtons();
        }

        // Actualizar interfaz
        Interface::updateLabels();
        Interface::updateButtons();
        Macro::updateTPS();

        // Reset completado silenciosamente

        // Retornar false para evitar que se procese más
        return false;
    }

    // if (key == enumKeyCodes::KEY_L && !isKeyRepeat && isKeyDown) {
    // }

    // if (key == enumKeyCodes::KEY_F && !isKeyRepeat && isKeyDown && PlayLayer::get()) {
    //   log::debug("POS DEBUG {}", PlayLayer::get()->m_player1->getPosition());
    //   log::debug("POS2 DEBUG {}", PlayLayer::get()->m_player2->getPosition());
    // }

    // if (key == enumKeyCodes::KEY_J && !isKeyRepeat && isKeyDown && PlayLayer::get()) {
    //   std::string str = ZipUtils::decompressString(PlayLayer::get()->m_level->m_levelString.c_str(), true, 0);
    //   log::debug("{}", str);
    // }

    return CCKeyboardDispatcher::dispatchKeyboardMSG(key, isKeyDown, isKeyRepeat);
  }
};

#ifdef GEODE_IS_ANDROID

namespace keybinds {

  struct ActionID {};

};

#endif

using namespace keybinds;

void onKeybind(bool down, ActionID id) {
#ifdef GEODE_IS_WINDOWS

  auto& g = Global::get();

  if (!down || (LevelEditorLayer::get() && !g.mod->getSettingValue<bool>("editor_keybinds")) || g.mod->getSettingValue<bool>("disable_keybinds"))
    return;

  if (g.state != state::recording && g.mod->getSettingValue<bool>("recording_only_keybinds"))
    return;

  if (id == "open_menu"_spr) {
    if (g.layer) {
      static_cast<RecordLayer*>(g.layer)->onClose(nullptr);
      return;
    }

    RecordLayer::openMenu();
  }

  // Keybinds handlers deshabilitados - usando interceptación directa
  /*if (id == "toggle_recording"_spr) {
    // Código movido a interceptación directa
  }

  if (id == "toggle_playing"_spr) {
    // Código movido a interceptación directa
  }*/

  if (id == "toggle_frame_stepper"_spr && PlayLayer::get())
    Global::toggleFrameStepper();

  if (id == "step_frame"_spr)
    Global::frameStep();

  if (id == "toggle_speedhack"_spr)
    Global::toggleSpeedhack();

  if (id == "show_trajectory"_spr) {
    g.mod->setSavedValue("macro_show_trajectory", !g.mod->getSavedValue<bool>("macro_show_trajectory"));

    if (g.layer) {
      if (static_cast<RecordLayer*>(g.layer)->trajectoryToggle)
        static_cast<RecordLayer*>(g.layer)->trajectoryToggle->toggle(g.mod->getSavedValue<bool>("macro_show_trajectory"));
    }

    g.showTrajectory = g.mod->getSavedValue<bool>("macro_show_trajectory");
    if (!g.showTrajectory) ShowTrajectory::trajectoryOff();
  }

  if (id == "toggle_render"_spr && PlayLayer::get()) {
    bool result = Renderer::toggle();

    if (result && Global::get().renderer.recording)
      Notification::create("Started Rendering", NotificationIcon::Info)->show();

    if (g.layer) {
      if (static_cast<RecordLayer*>(g.layer)->renderToggle)
        static_cast<RecordLayer*>(g.layer)->renderToggle->toggle(Global::get().renderer.recording);
    }

  }

  if (id == "toggle_noclip"_spr) {
    g.mod->setSavedValue("macro_noclip", !g.mod->getSavedValue<bool>("macro_noclip"));

    if (g.layer) {
      if (static_cast<RecordLayer*>(g.layer)->noclipToggle)
        static_cast<RecordLayer*>(g.layer)->noclipToggle->toggle(g.mod->getSavedValue<bool>("macro_noclip"));
    }
  }

  // Keybind "reset_macro_system" deshabilitado - usando interceptación directa de tecla Q
  /*if (id == "reset_macro_system"_spr) {
    // Código movido a interceptación directa de tecla Q
  }*/

#endif

}

$execute{

  #ifdef GEODE_IS_WINDOWS

    BindManager * bm = BindManager::get();

    bm->registerBindable({
        "open_menu"_spr,
        "Open Menu",
        "Open Menu.",
        { Keybind::create(KEY_F, Modifier::Alt) },
        "Paimon Scaler Mod",
        false
    });

    // Keybinds deshabilitados - usando interceptación directa
    /*bm->registerBindable({
        "toggle_recording"_spr,
        "Record macro",
        "Toggles recording.",
        { Keybind::create(KEY_T) },
        "Paimon Scaler Mod",
        false
    });

    bm->registerBindable({
      "toggle_playing"_spr,
      "Play macro",
      "Toggles playing.",
      { Keybind::create(KEY_Y) },
      "Paimon Scaler Mod",
        false
    });*/

    bm->registerBindable({
      "toggle_speedhack"_spr,
      "Speedhack",
      "Toggles speedhack.",
      { Keybind::create(KEY_S, Modifier::Alt) },
      "Paimon Scaler Mod",
        false
    });

    bm->registerBindable({
      "toggle_noclip"_spr,
      "NoClip",
      "Toggles NoClip.",
      { Keybind::create(KEY_N, Modifier::Alt) },
      "Paimon Scaler Mod",
        false
    });

    bm->registerBindable({
      "toggle_frame_stepper"_spr,
      "Toggle Frame Stepper",
      "Toggles frame stepper..",
      { Keybind::create(KEY_C, Modifier::Alt) },
      "Paimon Scaler Mod",
      false
    });

    bm->registerBindable({
      "step_frame"_spr,
      "Advance frame",
      "Advances one frame if frame stepper is on.",
      { Keybind::create(KEY_V) },
      "Paimon Scaler Mod"
    });

    bm->setRepeatOptionsFor("step_frame"_spr, { true, 10, 450 });

    bm->registerBindable({
      "show_trajectory"_spr,
      "Show Trajectory",
      "Toggles Show Trajectory.",
      { Keybind::create(KEY_R, Modifier::Alt) },
      "Paimon Scaler Mod"
    });

    bm->registerBindable({
      "toggle_render"_spr,
      "Render",
      "Toggles rendering.",
      { Keybind::create(KEY_P, Modifier::Alt) },
      "Paimon Scaler Mod",
      false
    });

    // Keybind "reset_macro_system" deshabilitado - usando interceptación directa de tecla Q
    /*bm->registerBindable({
      "reset_macro_system"_spr,
      "Reset Macro System",
      "Completely resets the macro system (clears all data).",
      { Keybind::create(KEY_Q) },
      "Paimon Scaler Mod",
      false
    });*/

    for (int i = 0; i < keybindIDs.size(); i++) {
        new EventListener([=](InvokeBindEvent* event) { onKeybind(event->isDown(), event->getID()); return ListenerResult::Propagate;
        }, InvokeBindFilter(nullptr, (""_spr) + keybindIDs[i]));
    }

  #endif
}
