{"geode": "4.6.1", "gd": {"win": "2.2074", "android": "2.2074"}, "version": "2.0.0", "id": "paimon.scaler", "name": "Paimon Scaler Mod", "developer": "FlozWer", "description": "paimon", "dependencies": [{"id": "geode.custom-keybinds", "version": ">=v1.10.0", "importance": "required", "platforms": ["win"]}], "settings": {"button-setting": {"type": "custom:button", "name": "Open Menu"}, "macro_accuracy": {"type": "string", "name": "Accuracy", "default": "Vanilla", "one-of": ["Vanilla", "Input Fixes", "Frame Fixes"]}, "frame_offset": {"name": "Frame Offset", "description": "Adds a frame offset when recording or playing macros", "type": "int", "default": 0, "min": -8, "max": 8}, "endscreen_button": {"name": "Button at Endscreen", "description": "Shows the menu button at the endscreen.", "type": "bool", "default": false}, "ffmpeg_path": {"name": "ffmpeg.exe Path", "description": "The path for ffmpeg.exe, needed to use render.", "type": "file", "default": "", "platforms": ["win"]}, "macros_folder": {"name": "Macro Save Location", "type": "folder", "default": "{gd_dir}/macros"}, "autosaves_folder": {"name": "Auto Saves Location", "type": "folder", "default": "{gd_dir}/autosaves"}, "render_folder": {"name": "Render Save Location", "type": "folder", "default": "{gd_dir}/renders"}, "background_color": {"name": "Menu background color", "type": "color", "default": "#334499"}, "editor_keybinds": {"name": "Keybinds on editor", "description": "Enables keybinds while on the level editor.", "type": "bool", "default": false, "platforms": ["win"]}, "disable_keybinds": {"name": "Disable keybinds", "description": "Disables all keybinds.", "type": "bool", "default": false, "platforms": ["win"]}, "recording_only_keybinds": {"name": "Keybinds only while recording", "description": "Keybinds only work while recording a macro.", "type": "bool", "default": false, "platforms": ["win"]}, "level_settings_button": {"name": "Show Button At Settings", "description": "Shows the xdBot button in the level settings layer.", "type": "bool", "default": false}, "restore_page": {"name": "<PERSON><PERSON> Set<PERSON>s Page", "description": "Saves the page you were in when you closed the menu.", "type": "bool", "default": true}, "disable_speedhack": {"name": "Auto Disable Speedhack", "description": "Disables Speed<PERSON> when exiting a level.", "type": "bool", "default": true}, "frame_fixes_limit": {"name": "Frame Fixes Limit in FPS", "description": "Limits how many frame fixes per second there will be.", "type": "int", "default": 360, "min": 1}, "auto_stop_playing": {"name": "Auto Stop Playing", "description": "Stops playing the macro when it ends.", "type": "bool", "default": true}, "lock_delta": {"name": "Lock Delta", "description": "Locks the delta.", "type": "bool", "default": false}, "allow_stars_rewards": {"name": "Allow Stars & Rewards", "description": "Allows receiving stars and other rewards when completing levels (disables safe mode for rewards).", "type": "bool", "default": true}}, "resources": {"sprites": ["resources/GJ_commentSide2_001_White.png", "resources/GJ_commentTop2_001_White.png"], "files": ["resources/default_hold_click.mp3", "resources/default_release_click.mp3", "resources/default_hold_left.mp3", "resources/default_release_left.mp3", "resources/default_hold_right.mp3", "resources/default_release_right.mp3"]}, "tags": ["gameplay", "utility"]}