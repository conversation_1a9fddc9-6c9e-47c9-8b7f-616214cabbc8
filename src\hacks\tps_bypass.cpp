#include "../includes.hpp"
#include <Geode/modify/GJBaseGameLayer.hpp>

class $modify(GJBaseGameLayer) {

    void update(float dt) {
        auto& g = Global::get();

        if (!g.tpsEnabled) return GJBaseGameLayer::update(dt);
        if (Global::getTPS() == 240.f) return GJBaseGameLayer::update(dt);
        if (!PlayLayer::get()) return GJBaseGameLayer::update(dt);

        float newDt = 1.f / Global::getTPS();

        if (g.frameStepper) return GJBaseGameLayer::update(newDt);

        // Optimización: limitar multiplicador para evitar bucles largos
        float realDt = dt + g.leftOver;
        if (realDt > dt && newDt < dt) realDt = dt;

        int mult = static_cast<int>(realDt / newDt);
        mult = std::min(mult, 4); // Máximo 4 iteraciones para evitar lag

        for (int i = 0; i < mult; ++i) {
            GJBaseGameLayer::update(newDt);
        }

        g.leftOver += (dt - newDt * mult);

    }

    float getModifiedDelta(float dt) {
        if (!Global::get().tpsEnabled) return GJBaseGameLayer::getModifiedDelta(dt);
        if (Global::getTPS() == 240.f) return GJBaseGameLayer::getModifiedDelta(dt);
        if (!PlayLayer::get()) return GJBaseGameLayer::getModifiedDelta(dt);

        double dVar1;
        float fVar2;
        float fVar3;
        double dVar4;

        float newDt = 1.f / Global::getTPS();
        
        if (0 < m_resumeTimer) {
            // cocos2d::CCDirector::sharedDirector();
            m_resumeTimer--;
            dt = 0.0;
        }

        fVar2 = 1.0;
        if (m_gameState.m_timeWarp <= 1.0) {
            fVar2 = m_gameState.m_timeWarp;
        }

        dVar1 = dt + m_extraDelta;
        fVar3 = std::round(dVar1 / (fVar2 * newDt));
        dVar4 = fVar3 * fVar2 * newDt;
        m_extraDelta = dVar1 - dVar4;

        return dVar4;
    }

};