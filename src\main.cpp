#include "includes.hpp"

#include "ui/record_layer.hpp"
#include "ui/game_ui.hpp"
#include "practice_fixes/practice_fixes.hpp"
#include "hacks/layout_mode.hpp"

#include <Geode/modify/GJBaseGameLayer.hpp>
#include <Geode/modify/PlayLayer.hpp>
#include <Geode/modify/PauseLayer.hpp>
#include <random>

$execute {

  geode::listenForSettingChanges("macro_accuracy", +[](std::string value) {
    auto& g = Global::get();

    // Deshabilitar todos los fixes para comportamiento natural como MegaHack
    g.frameFixes = false;
    g.inputFixes = false;

    // No activar fixes sin importar la configuración - solo clicks puros
    // if (value == "Frame Fixes") g.frameFixes = true;
    // if (value == "Input Fixes") g.inputFixes = true;
  });

  geode::listenForSettingChanges("frame_fixes_limit", +[](int64_t value) {
    Global::get().frameFixesLimit = value;
  });

  geode::listenForSettingChanges("lock_delta", +[](bool value) {
    Global::get().lockDelta = value;
  });

  geode::listenForSettingChanges("auto_stop_playing", +[](bool value) {
    Global::get().stopPlaying = value;
  });

};

class $modify(PlayLayer) {

  struct Fields {
    int delayedLevelRestart = -1;
  };

  void postUpdate(float dt) {
    PlayLayer::postUpdate(dt);
    auto& g = Global::get();

    if (m_fields->delayedLevelRestart != -1 && m_fields->delayedLevelRestart >= Global::getCurrentFrame()) {
      m_fields->delayedLevelRestart = -1;
      resetLevelFromStart();
    }

    // Forzar desactivación del Safe Mode si está configurado para permitir estrellas
    Global::forceSafeModeOff();

  }

  void onQuit() {
    if (Mod::get()->getSettingValue<bool>("disable_speedhack") && Global::get().speedhackEnabled)
      Global::toggleSpeedhack();

    PlayLayer::onQuit();
  }

  void pauseGame(bool b1) {
    Global::updateKeybinds();

    if (!Global::get().renderer.tryPause()) return;

    auto& g = Global::get();

    if (!m_player1 || !m_player2) return PlayLayer::pauseGame(b1);

    if (g.state != state::recording) return PlayLayer::pauseGame(b1);

    g.ignoreRecordAction = true;
    int frame = Global::getCurrentFrame() + 1;

    if (m_player1->m_holdingButtons[1]) {
      handleButton(false, 1, false);
      g.macro.inputs.push_back(input(frame, 1, false, false));
    }
    if (m_levelSettings->m_platformerMode) {
      if (m_player1->m_holdingButtons[2]) {
        handleButton(false, 2, false);
        g.macro.inputs.push_back(input(frame, 2, false, false));
      }
      if (m_player1->m_holdingButtons[3]) {
        handleButton(false, 3, false);
        g.macro.inputs.push_back(input(frame, 3, false, false));
      }
    }

    if (m_levelSettings->m_twoPlayerMode) {
      if (m_player2->m_holdingButtons[1]) {
        handleButton(false, 1, true);
        g.macro.inputs.push_back(input(frame, 1, true, false));
      }
      if (m_levelSettings->m_platformerMode) {
        if (m_player2->m_holdingButtons[2]) {
          handleButton(false, 2, false);
          g.macro.inputs.push_back(input(frame, 2, true, false));
        }
        if (m_player2->m_holdingButtons[3]) {
          handleButton(false, 3, false);
          g.macro.inputs.push_back(input(frame, 3, true, false));
        }
      }
    }

    g.ignoreRecordAction = false;

    PlayLayer::pauseGame(b1);
  }

  bool init(GJGameLevel * level, bool b1, bool b2) {
    auto& g = Global::get();
    g.firstAttempt = true;  

    if (!PlayLayer::init(level, b1, b2)) return false;

    Global::updateKeybinds();

    auto now = std::chrono::system_clock::now();
    g.currentSession = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    g.lastAutoSaveFrame = 0;

    return true;
  }

  void resetLevel() {
    auto& g = Global::get();

    // Guardar el número de intentos actual antes del reset
    int savedAttempts = m_attempts;
    bool shouldPreserveAttempts = false;

    // Verificar si estamos iniciando grabación (flag especial)
    if (g.mod->getSavedValue<bool>("_paimon_starting_recording", false)) {
        shouldPreserveAttempts = true;
        g.mod->setSavedValue("_paimon_starting_recording", false);
    }

    PlayLayer::resetLevel();

    // Si estamos empezando a grabar, restaurar el número de intentos
    if (shouldPreserveAttempts && !m_isPracticeMode) {
        m_attempts = savedAttempts;
    }

    int frame = Global::getCurrentFrame();

    if (!m_isPracticeMode)
      g.renderer.levelStartFrame = frame;

    if (g.restart && m_levelSettings->m_platformerMode && g.state != state::none)
      m_fields->delayedLevelRestart = frame + 2;

    Global::updateSeed(true);

    g.safeMode = false;

    // No activar Safe Mode en Layout Mode para permitir estrellas siempre
    if (g.layoutMode)
      g.safeMode = false;

    g.currentAction = 0;
    g.currentFrameFix = 0;
    g.restart = false;

    if (g.state == state::recording)
      Macro::updateInfo(this);

    if ((!m_isPracticeMode || frame <= 1 || g.checkpoints.empty()) && g.state == state::recording) {
      g.macro.inputs.clear();
      g.macro.frameFixes.clear();
      g.checkpoints.clear();

      g.macro.framerate = 240.f;
      if (g.layer) static_cast<RecordLayer*>(g.layer)->updateTPS();

      // No aplicar fixes complejos - dejar que el juego funcione naturalmente como MegaHack
      // PlayerData p1Data = PlayerPracticeFixes::saveData(m_player1);
      // PlayerData p2Data = PlayerPracticeFixes::saveData(m_player2);
      // InputPracticeFixes::applyFixes(this, p1Data, p2Data, frame);

      // INICIALIZAR PERSONALIDAD HUMANA ÚNICA
      std::random_device rd;
      std::mt19937 gen(rd());

      // Resetear estado emocional para nuevo intento
      g.humanBehavior.consecutiveAttempts = 0;
      g.humanBehavior.recentDeaths = 0;
      g.humanBehavior.stressLevel = std::uniform_real_distribution<>(0.0f, 0.4f)(gen); // Estrés inicial más variable
      g.humanBehavior.inPanicMode = false;
      g.humanBehavior.panicFramesLeft = 0;

      // Generar personalidad única más diversa
      g.humanBehavior.tendsToSpam = std::uniform_real_distribution<>(0.0f, 1.0f)(gen) < 0.25f; // 25% spammer
      g.humanBehavior.tendsToHold = std::uniform_real_distribution<>(0.0f, 1.0f)(gen) < 0.35f; // 35% holder
      g.humanBehavior.anticipationBias = std::uniform_real_distribution<>(-0.3f, 0.3f)(gen); // Bias más amplio
      g.humanBehavior.reactionTimeMs = std::uniform_real_distribution<>(100.0f, 250.0f)(gen); // Rango más amplio

      // Características adicionales únicas
      std::uniform_real_distribution<> personalityDis(0.0f, 1.0f);

      // Tipo de jugador (afecta comportamiento general)
      if (personalityDis(gen) < 0.2f) {
        // Jugador nervioso (20%)
        g.humanBehavior.stressLevel += 0.2f;
        g.humanBehavior.tendsToSpam = true;
        g.humanBehavior.reactionTimeMs += 30.0f;
      } else if (personalityDis(gen) < 0.4f) {
        // Jugador calmado (20%)
        g.humanBehavior.stressLevel *= 0.5f;
        g.humanBehavior.reactionTimeMs -= 20.0f;
      } else if (personalityDis(gen) < 0.6f) {
        // Jugador agresivo (20%)
        g.humanBehavior.tendsToSpam = true;
        g.humanBehavior.anticipationBias -= 0.1f; // Clickea antes
      } else if (personalityDis(gen) < 0.8f) {
        // Jugador cauteloso (20%)
        g.humanBehavior.tendsToHold = true;
        g.humanBehavior.anticipationBias += 0.1f; // Clickea después
      }
      // 20% jugador normal (sin modificaciones)

      Macro::resetVariables();

      m_player1->m_holdingRight = false;
      m_player1->m_holdingLeft = false;
      m_player2->m_holdingRight = false;
      m_player2->m_holdingLeft = false;

      m_player1->m_holdingButtons[2] = false;
      m_player1->m_holdingButtons[3] = false;
      m_player2->m_holdingButtons[2] = false;
      m_player2->m_holdingButtons[3] = false;
    }

    if (!m_levelSettings->m_platformerMode || (!g.mod->getSavedValue<bool>("macro_always_practice_fixes") && g.state != state::recording)) return;

    g.ignoreRecordAction = true;
    for (int i = 0; i < 4; i++) {
      bool player2 = !(sidesButtons[i] > 2);
      bool rightKey = sidesButtons[i] == 5 || sidesButtons[i] == 2;
      if (g.heldButtons[sidesButtons[i]])
        handleButton(true, indexButton[sidesButtons[i]], player2);
    }
    g.ignoreRecordAction = false;
  }

};

class $modify(BGLHook, GJBaseGameLayer) {

  struct Fields {
    bool macroInput = false;
    bool userInputActive = false;
    int userInputFrame = -1;
    int userInputButton = -1;
    bool userInputPlayer2 = false;
  };

  // Función simplificada para predecir peligro basada en física del jugador
  bool willCollideWithoutClick(int frame, int button, bool player2, bool down, int framesToPredict = 15) {
    auto& g = Global::get();
    PlayLayer* pl = PlayLayer::get();
    if (!pl || !g.useTrajectoryGuide) return false;

    PlayerObject* player = player2 ? pl->m_player2 : pl->m_player1;
    if (!player || player->m_isDead) return false;

    // Análisis simplificado basado en física del jugador
    float yVelocity = player->m_yVelocity;
    float yPosition = player->getPositionY();
    bool isOnGround = player->m_isOnGround;

    // Si está en el suelo y no es un click, probablemente no colisionará pronto
    if (isOnGround && down) return false;

    // Predecir posición futura basada en velocidad actual
    float futureY = yPosition + (yVelocity * framesToPredict * (1.0f/240.0f));

    // Si va a estar muy cerca del suelo y cayendo rápido
    if (futureY < 50.0f && yVelocity < -400.0f) {
      return true; // Probable colisión
    }

    // Si está cayendo muy rápido
    if (yVelocity < -800.0f) {
      return true; // Probable colisión por velocidad extrema
    }

    // Si está muy cerca del suelo
    if (yPosition < 80.0f && yVelocity < -200.0f) {
      return true; // Probable colisión por proximidad
    }

    return false; // Probablemente seguro
  }

  // Función para determinar urgencia del click basado en trayectoria (MÁS CONSERVADORA)
  int getClickUrgency(int frame, int button, bool player2, bool down) {
    auto& g = Global::get();
    PlayLayer* pl = PlayLayer::get();
    if (!pl || !g.useTrajectoryGuide) return 2; // Urgencia alta por defecto

    PlayerObject* player = player2 ? pl->m_player2 : pl->m_player1;
    if (!player || player->m_isDead) return 2;

    // Si es un release, siempre es crítico
    if (!down) return 3; // Máxima urgencia

    // Verificar si colisionará pronto sin el click
    if (willCollideWithoutClick(frame, button, player2, down, 5)) {
      return 3; // Máxima urgencia - colisión inminente
    }

    if (willCollideWithoutClick(frame, button, player2, down, 10)) {
      return 2; // Alta urgencia - colisión cercana
    }

    // SER MÁS CONSERVADOR: La mayoría de clicks son importantes
    if (willCollideWithoutClick(frame, button, player2, down, 20)) {
      return 2; // Alta urgencia - colisión lejana (cambiado de 1 a 2)
    }

    // Solo considerar innecesarios los clicks muy seguidos
    static int lastClickFrame = -10;
    int framesSinceLastClick = frame - lastClickFrame;
    lastClickFrame = frame;

    if (framesSinceLastClick < 3) {
      return 1; // Urgencia normal - posible spam
    }

    return 2; // Por defecto, alta urgencia (más conservador)
  }

  // Función mejorada para analizar si un click es necesario (MÁS PERMISIVA)
  bool isClickNecessaryByTrajectory(int frame, int button, bool player2, bool down) {
    auto& g = Global::get();
    PlayLayer* pl = PlayLayer::get();
    if (!pl || !g.useTrajectoryGuide) return true; // Si no hay trayectoria, usar click original

    PlayerObject* player = player2 ? pl->m_player2 : pl->m_player1;
    if (!player || player->m_isDead) return true;

    // Obtener urgencia basada en trayectoria
    int urgency = getClickUrgency(frame, button, player2, down);

    // TODOS los clicks de urgencia 1+ son necesarios
    if (urgency >= 1) return true;

    // Solo los clicks de urgencia 0 pueden ser innecesarios
    // Y solo si son spam muy obvio
    if (urgency == 0) {
      static int lastClickFrame = -10;
      int framesSinceLastClick = frame - lastClickFrame;
      lastClickFrame = frame;

      // Solo considerar innecesario si hay clicks muy seguidos
      if (framesSinceLastClick < 2) {
        // 70% probabilidad de que sea necesario (más conservador)
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 9);
        return dis(gen) < 7; // 70% probabilidad
      }
    }

    return true; // Por defecto, SIEMPRE necesario
  }

  // Función para actualizar comportamiento humano
  void updateHumanBehavior(int frame) {
    auto& g = Global::get();
    auto& h = g.humanBehavior;
    PlayLayer* pl = PlayLayer::get();
    if (!pl) return;

    h.totalPlayTime++;

    // Actualizar fatiga gradualmente
    h.fatigueLevel = std::min(1.0f, h.fatigueLevel + 0.00001f);

    // Detectar muerte para aumentar estrés
    if (pl->m_player1->m_isDead || (pl->m_player2 && pl->m_player2->m_isDead)) {
      h.recentDeaths++;
      h.stressLevel = std::min(1.0f, h.stressLevel + 0.1f);
      h.consecutiveAttempts++;

      // Entrar en modo pánico después de varias muertes
      if (h.recentDeaths >= 3) {
        h.inPanicMode = true;
        h.panicFramesLeft = 300; // 5 segundos de pánico
      }
    }

    // Reducir estrés gradualmente
    h.stressLevel = std::max(0.0f, h.stressLevel - 0.0001f);

    // Reducir pánico
    if (h.inPanicMode) {
      h.panicFramesLeft--;
      if (h.panicFramesLeft <= 0) {
        h.inPanicMode = false;
      }
    }

    // Simular aprendizaje (mejora con intentos)
    h.learningProgress = std::min(1.0f, h.consecutiveAttempts * 0.01f);

    // Ajustar tiempo de reacción basado en estado
    float baseReaction = 150.0f; // ms
    h.reactionTimeMs = baseReaction + (h.fatigueLevel * 100.0f) + (h.stressLevel * 80.0f);
  }

  // Función para simular errores humanos (MUY CONSERVADORA)
  bool shouldMakeHumanError(int frame, bool isImportantClick) {
    auto& g = Global::get();
    auto& h = g.humanBehavior;

    // SOLO errores muy ocasionales en clicks no críticos
    if (isImportantClick) return false; // NUNCA fallar clicks importantes

    // Probabilidad muy baja de error solo en clicks innecesarios
    float errorProbability = 0.005f; // 0.5% base (muy bajo)

    // Solo aumentar ligeramente con fatiga extrema
    if (h.fatigueLevel > 0.8f) {
      errorProbability += 0.01f; // Máximo 1.5%
    }

    // En modo pánico, solo errores muy ocasionales
    if (h.inPanicMode) {
      errorProbability += 0.02f; // Máximo 3.5%
    }

    // Generar error aleatorio
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(0.0, 1.0);

    return dis(gen) < errorProbability;
  }

  // Función para calcular delay humano realista (OPTIMIZADA)
  int calculateHumanDelay(int frame, bool isEmergency) {
    auto& g = Global::get();
    auto& h = g.humanBehavior;

    // Delays muy pequeños para mantener funcionalidad
    int baseDelay = isEmergency ? 0 : 1; // Emergencias inmediatas, otros 1 frame

    // Solo agregar delay adicional ocasionalmente
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 9);

    // 30% probabilidad de agregar 1-2 frames de delay (humanización sutil)
    if (dis(gen) < 3 && !isEmergency) {
      std::uniform_int_distribution<> delayDis(1, 2);
      baseDelay += delayDis(gen);
    }

    // En modo pánico, reacciones más rápidas (menos delay)
    if (h.inPanicMode && !isEmergency) {
      baseDelay = std::max(0, baseDelay - 1);
    }

    return std::max(0, std::min(baseDelay, 5)); // Máximo 5 frames de delay
  }

  // Función inteligente para sugerir clicks adicionales basada en trayectoria
  bool shouldAddExtraClick(int frame, bool player2) {
    auto& g = Global::get();
    auto& h = g.humanBehavior;
    PlayLayer* pl = PlayLayer::get();
    if (!pl || !g.useTrajectoryGuide) return false;

    PlayerObject* player = player2 ? pl->m_player2 : pl->m_player1;
    if (!player || player->m_isDead) return false;

    // ANÁLISIS AVANZADO DE TRAYECTORIA: Predecir colisión inminente
    bool willCollideVerySoon = willCollideWithoutClick(frame, 1, player2, true, 3); // 3 frames
    bool willCollideSoon = willCollideWithoutClick(frame, 1, player2, true, 8); // 8 frames

    // EMERGENCIA CRÍTICA: Colisión en 3 frames o menos
    if (willCollideVerySoon) {
      return true; // SIEMPRE agregar click de emergencia
    }

    // PELIGRO CERCANO: Colisión en 8 frames o menos
    if (willCollideSoon) {
      // En modo pánico, siempre clickear
      if (h.inPanicMode) {
        return true;
      }

      // Normalmente, alta probabilidad basada en estrés
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_real_distribution<> dis(0.0, 1.0);

      float emergencyProbability = 0.8f + (h.stressLevel * 0.2f); // 80-100%
      return dis(gen) < emergencyProbability;
    }

    // ANÁLISIS ADICIONAL: Situaciones de riesgo
    float yVelocity = player->m_yVelocity;
    float yPosition = player->getPositionY();

    // Caída muy rápida hacia el suelo
    bool fastFalling = (yVelocity < -600.0f) && (yPosition < 200.0f);

    // Muy cerca del suelo y cayendo
    bool nearGround = (yPosition < 80.0f) && (yVelocity < -300.0f);

    if (fastFalling || nearGround) {
      // Probabilidad basada en estado emocional
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_real_distribution<> dis(0.0, 1.0);

      float panicProbability = 0.6f + (h.stressLevel * 0.3f); // 60-90%

      // En modo pánico, más probable
      if (h.inPanicMode) {
        panicProbability = 0.9f;
      }

      return dis(gen) < panicProbability;
    }

    // Clicks de spam ocasionales solo si es propenso al spam
    if (h.tendsToSpam && h.stressLevel > 0.5f) {
      std::random_device rd;
      std::mt19937 gen(rd());
      std::uniform_real_distribution<> dis(0.0, 1.0);
      return dis(gen) < 0.05f; // 5% probabilidad muy baja
    }

    return false;
  }

  void processCommands(float dt) {
    auto& g = Global::get();

    PlayLayer* pl = PlayLayer::get();

    if (!pl) {
      // handlePlaying(Global::getCurrentFrame(true));
      // log::debug("{}", Global::getCurrentFrame(true));
      return GJBaseGameLayer::processCommands(dt);
    }

    Global::updateSeed();

    bool rendering = g.renderer.recording || g.renderer.recordingAudio;

    if (g.state != state::none || rendering) {

      if (!g.firstAttempt) {
        g.renderer.dontRender = false;
        g.renderer.dontRecordAudio = false;
      }

      int frame = Global::getCurrentFrame();
      if (frame > 2 && g.firstAttempt && g.macro.xdBotMacro) {
        g.firstAttempt = false;

        if ((m_levelSettings->m_platformerMode || rendering) && !m_levelEndAnimationStarted)
          return pl->resetLevelFromStart();
        else if (!m_levelEndAnimationStarted)
          return pl->resetLevel();
      }

      if (g.previousFrame == frame && frame != 0 && g.macro.xdBotMacro)
        return GJBaseGameLayer::processCommands(dt);

    }

    GJBaseGameLayer::processCommands(dt);

    if (g.state == state::none)
      return;

    int frame = Global::getCurrentFrame();
    g.previousFrame = frame;

    if (g.macro.xdBotMacro && g.restart && !m_levelEndAnimationStarted) {
      if ((m_levelSettings->m_platformerMode && g.state != state::none) || g.renderer.recording || g.renderer.recordingAudio)
        return pl->resetLevelFromStart();
      else
        return pl->resetLevel();
    }

    if (g.state == state::recording)
      handleRecording(frame);

    if (g.state == state::playing)
      handlePlaying(Global::getCurrentFrame());

  }

  void handleRecording(int frame) {
    auto& g = Global::get();

    if (g.ignoreFrame != -1) {
      if (g.ignoreFrame < frame) g.ignoreFrame = -1;
    }

    bool twoPlayers = m_levelSettings->m_twoPlayerMode;

    if (g.delayedFrameInput[0] == frame) {
      g.delayedFrameInput[0] = -1;
      // if ((g.heldButtons[0] && twoPlayers) || (!twoPlayers && (g.heldButtons[0] || g.heldButtons[3])))
        GJBaseGameLayer::handleButton(true, 1, true);
    }

    if (g.delayedFrameInput[1] == frame) {
      g.delayedFrameInput[1] = -1;
      // if ((g.heldButtons[3] && twoPlayers) || (!twoPlayers && (g.heldButtons[0] || g.heldButtons[3])))
        GJBaseGameLayer::handleButton(true, 1, false);
    }

    if (frame > g.ignoreJumpButton && g.ignoreJumpButton != -1)
      g.ignoreJumpButton = -1;

    for (int x = 0; x < 2; x++) {
      if (g.delayedFrameReleaseMain[x] == frame) {
        bool player2 = x == 0;
        g.delayedFrameReleaseMain[x] = -1;
        GJBaseGameLayer::handleButton(false, 1, twoPlayers ? player2 : false);
      }

      if (!m_levelSettings->m_platformerMode)
        continue;

      for (int y = 0; y < 2; y++) {
        if (g.delayedFrameRelease[x][y] == frame) {
          int button = y == 0 ? 2 : 3;
          bool player2 = x == 0;
          g.delayedFrameRelease[x][y] = -1;
          GJBaseGameLayer::handleButton(false, button, player2);
        }
      }
    }

    // Deshabilitar grabación de frame fixes - solo grabar clicks
    // if (!g.frameFixes || g.macro.inputs.empty()) return;

    // if (!g.macro.frameFixes.empty())
    //   if (1.f / Global::getTPS() * (frame - g.macro.frameFixes.back().frame) < 1.f / g.frameFixesLimit)
    //     return;

    // g.macro.recordFrameFix(frame, m_player1, m_player2);

  }

  void handlePlaying(int frame) {
    auto& g = Global::get();
    if (m_levelEndAnimationStarted) return;

    if (m_player1->m_isDead) {
      m_player1->releaseAllButtons();
      m_player2->releaseAllButtons();
      return;
    }

    m_fields->macroInput = true;

    // Sistema exacto como MegaHack v8 - sin delays ni variaciones
    while (g.currentAction < g.macro.inputs.size() && frame >= g.macro.inputs[g.currentAction].frame) {
      auto input = g.macro.inputs[g.currentAction];

      if (frame != g.respawnFrame) {
        if (Macro::flipControls())
          input.player2 = !input.player2;

        // Ejecutar click exacto sin delays ni variaciones
        GJBaseGameLayer::handleButton(input.down, input.button, input.player2);
      }

      g.currentAction++;
      // No activar Safe Mode durante reproducción de macros para permitir estrellas siempre
      g.safeMode = false;
    }

    // Limpiar flags
    m_fields->userInputActive = false;
    g.respawnFrame = -1;
    m_fields->macroInput = false;

    // Verificar si terminó la reproducción
    if (g.currentAction == g.macro.inputs.size()) {
      if (g.stopPlaying) {
        Macro::togglePlaying();
        Macro::resetState(true);
        return;
      }
    }

  }

  void applyFrameFixes(int frame) {
    auto& g = Global::get();

    // No aplicar frame fixes durante la reproducción - solo durante grabación
    if (g.state == state::playing) return;

    if ((!g.frameFixes && !g.inputFixes) || !PlayLayer::get()) return;

    while (g.currentFrameFix < g.macro.frameFixes.size() && frame >= g.macro.frameFixes[g.currentFrameFix].frame) {
      auto& fix = g.macro.frameFixes[g.currentFrameFix];

      PlayerObject* p1 = m_player1;
      PlayerObject* p2 = m_player2;

      cocos2d::CCPoint pos1 = p1->getPosition();
      cocos2d::CCPoint pos2 = p2->getPosition();

      if (fix.p1.pos.x != 0.f && fix.p1.pos.y != 0.f)
        p1->setPosition(fix.p1.pos);
        
      if (fix.p1.rotate && fix.p1.rotation != 0.f)
        p1->setRotation(fix.p1.rotation);

      if (m_gameState.m_isDualMode) {
        if (fix.p2.pos.x != 0.f && fix.p2.pos.y != 0.f)
          p2->setPosition(fix.p2.pos);

        if (fix.p2.rotate && fix.p2.rotation != 0.f)
          p2->setRotation(fix.p2.rotation);
      }

      g.currentFrameFix++;
    }

  }

  void handleButton(bool hold, int button, bool player2) {
    auto& g = Global::get();

    if (g.p2mirror && m_gameState.m_isDualMode && !g.autoclicker) {
      GJBaseGameLayer::handleButton(g.mod->getSavedValue<bool>("p2_input_mirror_inverted") ? !hold : hold, button, !player2);
    }

    if (g.state == state::none)
      return GJBaseGameLayer::handleButton(hold, button, player2);

    if (g.state == state::playing) {
      // Permitir clicks del usuario durante reproducción con prioridad sobre el macro
      // Registrar el input del usuario para pausar temporalmente el macro
      m_fields->macroInput = false;
      m_fields->userInputActive = true;
      m_fields->userInputFrame = Global::getCurrentFrame();
      m_fields->userInputButton = button;
      m_fields->userInputPlayer2 = player2;
      return GJBaseGameLayer::handleButton(hold, button, player2);
    }
    else if (g.ignoreFrame != -1 && hold)
      return;

    int frame = Global::getCurrentFrame();

    if (frame >= 10 && hold)
      Global::hasIncompatibleMods();

    if (g.state != state::recording) return GJBaseGameLayer::handleButton(hold, button, player2);

    // Sistema simple como MegaHack - solo grabar y aplicar clicks directamente
    GJBaseGameLayer::handleButton(hold, button, player2);

    if (!m_levelSettings->m_twoPlayerMode)
      player2 = false;

    // Grabar el click de manera simple sin fixes complejos
    if (!g.ignoreRecordAction && !g.creatingTrajectory && !m_player1->m_isDead) {
      g.macro.recordAction(frame, button, player2, hold);
      if (g.p2mirror && m_gameState.m_isDualMode)
        g.macro.recordAction(frame, button, !player2, g.mod->getSavedValue<bool>("p2_input_mirror_inverted") ? !hold : hold);
    }

  }
};

class $modify(PauseLayer) {

  void onPracticeMode(CCObject * sender) {
    PauseLayer::onPracticeMode(sender);
    auto& g = Global::get();

    if (g.state != state::none) PlayLayer::get()->resetLevel();

    // Activar automáticamente la grabación cuando se entra en modo práctica
    if (g.state != state::recording) {
      g.state = state::recording;
      g.currentAction = 0;
      g.currentFrameFix = 0;
      g.restart = true;

      // Actualizar la interfaz
      Interface::updateLabels();
      Interface::updateButtons();
      Macro::updateTPS();

      g.lastAutoSaveMS = std::chrono::steady_clock::now();
    }
  }

  void onNormalMode(CCObject * sender) {
    PauseLayer::onNormalMode(sender);
    auto& g = Global::get();

    g.checkpoints.clear();

    // Detener la grabación cuando se sale del modo práctica
    if (g.state == state::recording) {
      g.state = state::none;

      // Actualizar la interfaz
      Interface::updateLabels();
      Interface::updateButtons();
      Macro::updateTPS();
    }

    if (g.restart) {
      if (PlayLayer* pl = PlayLayer::get())
        pl->resetLevel();
    }

  }

  void onQuit(CCObject * sender) {
    PauseLayer::onQuit(sender);

    Macro::resetState();

    Loader::get()->queueInMainThread([] {
      auto& g = Global::get();
      if (g.renderer.recording) g.renderer.stop();
      if (g.renderer.recordingAudio) g.renderer.stopAudio();
    });
  }

  void goEdit() {
    PauseLayer::goEdit();

    Macro::resetState();
    
    Loader::get()->queueInMainThread([] {
      auto& g = Global::get();
      if (g.renderer.recording) g.renderer.stop();
      if (g.renderer.recordingAudio) g.renderer.stopAudio();
    });
  }

};
