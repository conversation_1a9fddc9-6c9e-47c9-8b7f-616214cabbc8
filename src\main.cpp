#include "includes.hpp"

#include "ui/record_layer.hpp"
#include "ui/game_ui.hpp"
#include "practice_fixes/practice_fixes.hpp"
#include "hacks/layout_mode.hpp"

#include <Geode/modify/GJBaseGameLayer.hpp>
#include <Geode/modify/PlayLayer.hpp>
#include <Geode/modify/PauseLayer.hpp>
#include <random>

$execute {

  geode::listenForSettingChanges("macro_accuracy", +[](std::string value) {
    auto& g = Global::get();

    // Deshabilitar todos los fixes para comportamiento natural como MegaHack
    g.frameFixes = false;
    g.inputFixes = false;

    // No activar fixes sin importar la configuración - solo clicks puros
    // if (value == "Frame Fixes") g.frameFixes = true;
    // if (value == "Input Fixes") g.inputFixes = true;
  });

  geode::listenForSettingChanges("frame_fixes_limit", +[](int64_t value) {
    Global::get().frameFixesLimit = value;
  });

  geode::listenForSettingChanges("lock_delta", +[](bool value) {
    Global::get().lockDelta = value;
  });

  geode::listenForSettingChanges("auto_stop_playing", +[](bool value) {
    Global::get().stopPlaying = value;
  });

};

class $modify(PlayLayer) {

  struct Fields {
    int delayedLevelRestart = -1;
  };

  void postUpdate(float dt) {
    PlayLayer::postUpdate(dt);
    auto& g = Global::get();

    if (m_fields->delayedLevelRestart != -1 && m_fields->delayedLevelRestart >= Global::getCurrentFrame()) {
      m_fields->delayedLevelRestart = -1;
      resetLevelFromStart();
    }

    // Forzar desactivación del Safe Mode si está configurado para permitir estrellas
    Global::forceSafeModeOff();

  }

  void onQuit() {
    if (Mod::get()->getSettingValue<bool>("disable_speedhack") && Global::get().speedhackEnabled)
      Global::toggleSpeedhack();

    PlayLayer::onQuit();
  }

  void pauseGame(bool b1) {
    Global::updateKeybinds();

    if (!Global::get().renderer.tryPause()) return;

    auto& g = Global::get();

    if (!m_player1 || !m_player2) return PlayLayer::pauseGame(b1);

    if (g.state != state::recording) return PlayLayer::pauseGame(b1);

    g.ignoreRecordAction = true;
    int frame = Global::getCurrentFrame() + 1;

    if (m_player1->m_holdingButtons[1]) {
      handleButton(false, 1, false);
      g.macro.inputs.push_back(input(frame, 1, false, false));
    }
    if (m_levelSettings->m_platformerMode) {
      if (m_player1->m_holdingButtons[2]) {
        handleButton(false, 2, false);
        g.macro.inputs.push_back(input(frame, 2, false, false));
      }
      if (m_player1->m_holdingButtons[3]) {
        handleButton(false, 3, false);
        g.macro.inputs.push_back(input(frame, 3, false, false));
      }
    }

    if (m_levelSettings->m_twoPlayerMode) {
      if (m_player2->m_holdingButtons[1]) {
        handleButton(false, 1, true);
        g.macro.inputs.push_back(input(frame, 1, true, false));
      }
      if (m_levelSettings->m_platformerMode) {
        if (m_player2->m_holdingButtons[2]) {
          handleButton(false, 2, false);
          g.macro.inputs.push_back(input(frame, 2, true, false));
        }
        if (m_player2->m_holdingButtons[3]) {
          handleButton(false, 3, false);
          g.macro.inputs.push_back(input(frame, 3, true, false));
        }
      }
    }

    g.ignoreRecordAction = false;

    PlayLayer::pauseGame(b1);
  }

  bool init(GJGameLevel * level, bool b1, bool b2) {
    auto& g = Global::get();
    g.firstAttempt = true;  

    if (!PlayLayer::init(level, b1, b2)) return false;

    Global::updateKeybinds();

    auto now = std::chrono::system_clock::now();
    g.currentSession = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    g.lastAutoSaveFrame = 0;

    return true;
  }

  void resetLevel() {
    auto& g = Global::get();

    // Guardar el número de intentos actual antes del reset
    int savedAttempts = m_attempts;
    bool shouldPreserveAttempts = false;

    // Verificar si estamos iniciando grabación (flag especial)
    if (g.mod->getSavedValue<bool>("_paimon_starting_recording", false)) {
        shouldPreserveAttempts = true;
        g.mod->setSavedValue("_paimon_starting_recording", false);
    }

    PlayLayer::resetLevel();

    // Si estamos empezando a grabar, restaurar el número de intentos
    if (shouldPreserveAttempts && !m_isPracticeMode) {
        m_attempts = savedAttempts;
    }

    int frame = Global::getCurrentFrame();

    if (!m_isPracticeMode)
      g.renderer.levelStartFrame = frame;

    if (g.restart && m_levelSettings->m_platformerMode && g.state != state::none)
      m_fields->delayedLevelRestart = frame + 2;

    Global::updateSeed(true);

    g.safeMode = false;

    // No activar Safe Mode en Layout Mode para permitir estrellas siempre
    if (g.layoutMode)
      g.safeMode = false;

    g.currentAction = 0;
    g.currentFrameFix = 0;
    g.restart = false;

    if (g.state == state::recording)
      Macro::updateInfo(this);

    if ((!m_isPracticeMode || frame <= 1 || g.checkpoints.empty()) && g.state == state::recording) {
      g.macro.inputs.clear();
      g.macro.frameFixes.clear();
      g.checkpoints.clear();

      g.macro.framerate = 240.f;
      if (g.layer) static_cast<RecordLayer*>(g.layer)->updateTPS();

      // Sin fixes complejos - solo replay básico

      // Sin inicialización de comportamiento humano - solo replay básico

      Macro::resetVariables();

      m_player1->m_holdingRight = false;
      m_player1->m_holdingLeft = false;
      m_player2->m_holdingRight = false;
      m_player2->m_holdingLeft = false;

      m_player1->m_holdingButtons[2] = false;
      m_player1->m_holdingButtons[3] = false;
      m_player2->m_holdingButtons[2] = false;
      m_player2->m_holdingButtons[3] = false;
    }

    if (!m_levelSettings->m_platformerMode || (!g.mod->getSavedValue<bool>("macro_always_practice_fixes") && g.state != state::recording)) return;

    g.ignoreRecordAction = true;
    for (int i = 0; i < 4; i++) {
      bool player2 = !(sidesButtons[i] > 2);
      bool rightKey = sidesButtons[i] == 5 || sidesButtons[i] == 2;
      if (g.heldButtons[sidesButtons[i]])
        handleButton(true, indexButton[sidesButtons[i]], player2);
    }
    g.ignoreRecordAction = false;
  }

};

class $modify(BGLHook, GJBaseGameLayer) {

  struct Fields {
    bool macroInput = false;
    bool userInputActive = false;
    int userInputFrame = -1;
    int userInputButton = -1;
    bool userInputPlayer2 = false;
  };

  // Todas las funciones de comportamiento humano eliminadas para máximo rendimiento

  void processCommands(float dt) {
    auto& g = Global::get();

    PlayLayer* pl = PlayLayer::get();

    if (!pl) {
      return GJBaseGameLayer::processCommands(dt);
    }

    // Procesamiento básico sin complejidades
    GJBaseGameLayer::processCommands(dt);

    if (g.state == state::none)
      return;

    int frame = Global::getCurrentFrame();

    // Solo manejar grabación y reproducción básica
    if (g.state == state::recording)
      handleRecording(frame);

    if (g.state == state::playing)
      handlePlaying(frame);
  }

  void handleRecording(int frame) {
    auto& g = Global::get();

    // Sistema de grabación simplificado - solo manejar inputs básicos
    if (g.ignoreFrame != -1) {
      if (g.ignoreFrame < frame) g.ignoreFrame = -1;
    }

    // Sin delayed inputs complejos - solo grabación directa
  }

  void handlePlaying(int frame) {
    auto& g = Global::get();
    if (m_levelEndAnimationStarted) return;

    if (m_player1->m_isDead) {
      m_player1->releaseAllButtons();
      m_player2->releaseAllButtons();
      return;
    }

    // Sistema de replay básico - sin complejidades
    while (g.currentAction < g.macro.inputs.size() && frame >= g.macro.inputs[g.currentAction].frame) {
      auto input = g.macro.inputs[g.currentAction];

      if (frame != g.respawnFrame) {
        if (Macro::flipControls())
          input.player2 = !input.player2;

        GJBaseGameLayer::handleButton(input.down, input.button, input.player2);
      }

      g.currentAction++;
    }

    g.respawnFrame = -1;

    // Auto-stop cuando termine
    if (g.currentAction == g.macro.inputs.size() && g.stopPlaying) {
      Macro::togglePlaying();
      Macro::resetState(true);
    }
  }

  // Frame fixes eliminados para máximo rendimiento
  void applyFrameFixes(int frame) {
    // Sin frame fixes - solo replay básico
    return;
  }

  void handleButton(bool hold, int button, bool player2) {
    auto& g = Global::get();

    // Sin mirror ni autoclicker para simplicidad
    if (g.state == state::none)
      return GJBaseGameLayer::handleButton(hold, button, player2);

    if (g.state == state::playing) {
      // Permitir input del usuario durante reproducción
      return GJBaseGameLayer::handleButton(hold, button, player2);
    }

    if (g.state != state::recording)
      return GJBaseGameLayer::handleButton(hold, button, player2);

    // Sistema de grabación básico
    GJBaseGameLayer::handleButton(hold, button, player2);

    if (!m_levelSettings->m_twoPlayerMode)
      player2 = false;

    // Grabar el click directamente sin verificaciones complejas
    if (!g.ignoreRecordAction && !m_player1->m_isDead) {
      g.macro.recordAction(Global::getCurrentFrame(), button, player2, hold);
    }
  }
};

class $modify(PauseLayer) {

  void onPracticeMode(CCObject * sender) {
    PauseLayer::onPracticeMode(sender);
    auto& g = Global::get();

    if (g.state != state::none) PlayLayer::get()->resetLevel();

    // Activar automáticamente la grabación cuando se entra en modo práctica
    if (g.state != state::recording) {
      g.state = state::recording;
      g.currentAction = 0;
      g.currentFrameFix = 0;
      g.restart = true;

      // Actualizar la interfaz
      Interface::updateLabels();
      Interface::updateButtons();
      Macro::updateTPS();

      g.lastAutoSaveMS = std::chrono::steady_clock::now();
    }
  }

  void onNormalMode(CCObject * sender) {
    PauseLayer::onNormalMode(sender);
    auto& g = Global::get();

    g.checkpoints.clear();

    // Detener la grabación cuando se sale del modo práctica
    if (g.state == state::recording) {
      g.state = state::none;

      // Actualizar la interfaz
      Interface::updateLabels();
      Interface::updateButtons();
      Macro::updateTPS();
    }

    if (g.restart) {
      if (PlayLayer* pl = PlayLayer::get())
        pl->resetLevel();
    }

  }

  void onQuit(CCObject * sender) {
    PauseLayer::onQuit(sender);

    Macro::resetState();

    Loader::get()->queueInMainThread([] {
      auto& g = Global::get();
      if (g.renderer.recording) g.renderer.stop();
      if (g.renderer.recordingAudio) g.renderer.stopAudio();
    });
  }

  void goEdit() {
    PauseLayer::goEdit();

    Macro::resetState();
    
    Loader::get()->queueInMainThread([] {
      auto& g = Global::get();
      if (g.renderer.recording) g.renderer.stop();
      if (g.renderer.recordingAudio) g.renderer.stopAudio();
    });
  }

};
