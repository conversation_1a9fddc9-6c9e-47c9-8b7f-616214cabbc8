#include "includes.hpp"

#include "ui/record_layer.hpp"
#include "ui/game_ui.hpp"
#include "practice_fixes/practice_fixes.hpp"
#include "hacks/layout_mode.hpp"

#include <Geode/modify/GJBaseGameLayer.hpp>
#include <Geode/modify/PlayLayer.hpp>
#include <Geode/modify/PauseLayer.hpp>
#include <random>

$execute {

  geode::listenForSettingChanges("macro_accuracy", +[](std::string value) {
    auto& g = Global::get();

    // Deshabilitar todos los fixes para comportamiento natural como MegaHack
    g.frameFixes = false;
    g.inputFixes = false;

    // No activar fixes sin importar la configuración - solo clicks puros
    // if (value == "Frame Fixes") g.frameFixes = true;
    // if (value == "Input Fixes") g.inputFixes = true;
  });

  geode::listenForSettingChanges("frame_fixes_limit", +[](int64_t value) {
    Global::get().frameFixesLimit = value;
  });

  geode::listenForSettingChanges("lock_delta", +[](bool value) {
    Global::get().lockDelta = value;
  });

  geode::listenForSettingChanges("auto_stop_playing", +[](bool value) {
    Global::get().stopPlaying = value;
  });

};

class $modify(PlayLayer) {

  struct Fields {
    int delayedLevelRestart = -1;
  };

  void postUpdate(float dt) {
    PlayLayer::postUpdate(dt);
    auto& g = Global::get();

    if (m_fields->delayedLevelRestart != -1 && m_fields->delayedLevelRestart >= Global::getCurrentFrame()) {
      m_fields->delayedLevelRestart = -1;
      resetLevelFromStart();
    }

    // Forzar desactivación del Safe Mode si está configurado para permitir estrellas
    Global::forceSafeModeOff();

  }

  void onQuit() {
    if (Mod::get()->getSettingValue<bool>("disable_speedhack") && Global::get().speedhackEnabled)
      Global::toggleSpeedhack();

    PlayLayer::onQuit();
  }

  void pauseGame(bool b1) {
    Global::updateKeybinds();

    if (!Global::get().renderer.tryPause()) return;

    auto& g = Global::get();

    if (!m_player1 || !m_player2) return PlayLayer::pauseGame(b1);

    if (g.state != state::recording) return PlayLayer::pauseGame(b1);

    g.ignoreRecordAction = true;
    int frame = Global::getCurrentFrame() + 1;

    if (m_player1->m_holdingButtons[1]) {
      handleButton(false, 1, false);
      g.macro.inputs.push_back(input(frame, 1, false, false));
    }
    if (m_levelSettings->m_platformerMode) {
      if (m_player1->m_holdingButtons[2]) {
        handleButton(false, 2, false);
        g.macro.inputs.push_back(input(frame, 2, false, false));
      }
      if (m_player1->m_holdingButtons[3]) {
        handleButton(false, 3, false);
        g.macro.inputs.push_back(input(frame, 3, false, false));
      }
    }

    if (m_levelSettings->m_twoPlayerMode) {
      if (m_player2->m_holdingButtons[1]) {
        handleButton(false, 1, true);
        g.macro.inputs.push_back(input(frame, 1, true, false));
      }
      if (m_levelSettings->m_platformerMode) {
        if (m_player2->m_holdingButtons[2]) {
          handleButton(false, 2, false);
          g.macro.inputs.push_back(input(frame, 2, true, false));
        }
        if (m_player2->m_holdingButtons[3]) {
          handleButton(false, 3, false);
          g.macro.inputs.push_back(input(frame, 3, true, false));
        }
      }
    }

    g.ignoreRecordAction = false;

    PlayLayer::pauseGame(b1);
  }

  bool init(GJGameLevel * level, bool b1, bool b2) {
    auto& g = Global::get();
    g.firstAttempt = true;  

    if (!PlayLayer::init(level, b1, b2)) return false;

    Global::updateKeybinds();

    auto now = std::chrono::system_clock::now();
    g.currentSession = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    g.lastAutoSaveFrame = 0;

    return true;
  }

  void resetLevel() {
    auto& g = Global::get();

    // Guardar el número de intentos actual antes del reset
    int savedAttempts = m_attempts;
    bool shouldPreserveAttempts = false;

    // Verificar si estamos iniciando grabación (flag especial)
    if (g.mod->getSavedValue<bool>("_paimon_starting_recording", false)) {
        shouldPreserveAttempts = true;
        g.mod->setSavedValue("_paimon_starting_recording", false);
    }

    PlayLayer::resetLevel();

    // Si estamos empezando a grabar, restaurar el número de intentos
    if (shouldPreserveAttempts && !m_isPracticeMode) {
        m_attempts = savedAttempts;
    }

    int frame = Global::getCurrentFrame();

    if (!m_isPracticeMode)
      g.renderer.levelStartFrame = frame;

    if (g.restart && m_levelSettings->m_platformerMode && g.state != state::none)
      m_fields->delayedLevelRestart = frame + 2;

    Global::updateSeed(true);

    g.safeMode = false;

    // No activar Safe Mode en Layout Mode para permitir estrellas siempre
    if (g.layoutMode)
      g.safeMode = false;

    g.currentAction = 0;
    g.currentFrameFix = 0;
    g.restart = false;

    if (g.state == state::recording)
      Macro::updateInfo(this);

    if ((!m_isPracticeMode || frame <= 1 || g.checkpoints.empty()) && g.state == state::recording) {
      g.macro.inputs.clear();
      g.macro.frameFixes.clear();
      g.checkpoints.clear();

      g.macro.framerate = 240.f;
      if (g.layer) static_cast<RecordLayer*>(g.layer)->updateTPS();

      // Sin fixes complejos - solo replay básico

      // Sin inicialización de comportamiento humano - solo replay básico

      Macro::resetVariables();

      m_player1->m_holdingRight = false;
      m_player1->m_holdingLeft = false;
      m_player2->m_holdingRight = false;
      m_player2->m_holdingLeft = false;

      m_player1->m_holdingButtons[2] = false;
      m_player1->m_holdingButtons[3] = false;
      m_player2->m_holdingButtons[2] = false;
      m_player2->m_holdingButtons[3] = false;
    }

    if (!m_levelSettings->m_platformerMode || (!g.mod->getSavedValue<bool>("macro_always_practice_fixes") && g.state != state::recording)) return;

    g.ignoreRecordAction = true;
    for (int i = 0; i < 4; i++) {
      bool player2 = !(sidesButtons[i] > 2);
      bool rightKey = sidesButtons[i] == 5 || sidesButtons[i] == 2;
      if (g.heldButtons[sidesButtons[i]])
        handleButton(true, indexButton[sidesButtons[i]], player2);
    }
    g.ignoreRecordAction = false;
  }

};

class $modify(BGLHook, GJBaseGameLayer) {

  struct Fields {
    bool macroInput = false;
    bool userInputActive = false;
    int userInputFrame = -1;
    int userInputButton = -1;
    bool userInputPlayer2 = false;
  };

  // Todas las funciones de comportamiento humano eliminadas para máximo rendimiento

  void processCommands(float dt) {
    auto& g = Global::get();

    PlayLayer* pl = PlayLayer::get();

    if (!pl) {
      return GJBaseGameLayer::processCommands(dt);
    }

    Global::updateSeed();

    bool rendering = g.renderer.recording || g.renderer.recordingAudio;

    if (g.state != state::none || rendering) {

      if (!g.firstAttempt) {
        g.renderer.dontRender = false;
        g.renderer.dontRecordAudio = false;
      }

      int frame = Global::getCurrentFrame();
      if (frame > 2 && g.firstAttempt && g.macro.xdBotMacro) {
        g.firstAttempt = false;

        if ((m_levelSettings->m_platformerMode || rendering) && !m_levelEndAnimationStarted)
          return pl->resetLevelFromStart();
        else if (!m_levelEndAnimationStarted)
          return pl->resetLevel();
      }

      if (g.previousFrame == frame && frame != 0 && g.macro.xdBotMacro)
        return GJBaseGameLayer::processCommands(dt);

    }

    GJBaseGameLayer::processCommands(dt);

    if (g.state == state::none)
      return;

    int frame = Global::getCurrentFrame();
    g.previousFrame = frame;

    if (g.macro.xdBotMacro && g.restart && !m_levelEndAnimationStarted) {
      if ((m_levelSettings->m_platformerMode && g.state != state::none) || g.renderer.recording || g.renderer.recordingAudio)
        return pl->resetLevelFromStart();
      else
        return pl->resetLevel();
    }

    if (g.state == state::recording)
      handleRecording(frame);

    if (g.state == state::playing)
      handlePlaying(Global::getCurrentFrame());
  }

  void handleRecording(int frame) {
    auto& g = Global::get();

    if (g.ignoreFrame != -1) {
      if (g.ignoreFrame < frame) g.ignoreFrame = -1;
    }

    bool twoPlayers = m_levelSettings->m_twoPlayerMode;

    if (g.delayedFrameInput[0] == frame) {
      g.delayedFrameInput[0] = -1;
      GJBaseGameLayer::handleButton(true, 1, true);
    }

    if (g.delayedFrameInput[1] == frame) {
      g.delayedFrameInput[1] = -1;
      GJBaseGameLayer::handleButton(true, 1, false);
    }

    if (frame > g.ignoreJumpButton && g.ignoreJumpButton != -1)
      g.ignoreJumpButton = -1;

    for (int x = 0; x < 2; x++) {
      if (g.delayedFrameReleaseMain[x] == frame) {
        bool player2 = x == 0;
        g.delayedFrameReleaseMain[x] = -1;
        GJBaseGameLayer::handleButton(false, 1, twoPlayers ? player2 : false);
      }

      if (!m_levelSettings->m_platformerMode)
        continue;

      for (int y = 0; y < 2; y++) {
        if (g.delayedFrameRelease[x][y] == frame) {
          int button = y == 0 ? 2 : 3;
          bool player2 = x == 0;
          g.delayedFrameRelease[x][y] = -1;
          GJBaseGameLayer::handleButton(false, button, player2);
        }
      }
    }
  }

  void handlePlaying(int frame) {
    auto& g = Global::get();
    if (m_levelEndAnimationStarted) return;

    if (m_player1->m_isDead) {
      m_player1->releaseAllButtons();
      m_player2->releaseAllButtons();
      return;
    }

    m_fields->macroInput = true;

    // Sistema exacto como MegaHack v8 - sin delays ni variaciones
    while (g.currentAction < g.macro.inputs.size() && frame >= g.macro.inputs[g.currentAction].frame) {
      auto input = g.macro.inputs[g.currentAction];

      if (frame != g.respawnFrame) {
        if (Macro::flipControls())
          input.player2 = !input.player2;

        // Ejecutar click exacto sin delays ni variaciones
        GJBaseGameLayer::handleButton(input.down, input.button, input.player2);
      }

      g.currentAction++;
      // No activar Safe Mode durante reproducción de macros para permitir estrellas siempre
      g.safeMode = false;
    }

    // Limpiar flags
    m_fields->userInputActive = false;
    g.respawnFrame = -1;
    m_fields->macroInput = false;

    // Verificar si terminó la reproducción
    if (g.currentAction == g.macro.inputs.size()) {
      if (g.stopPlaying) {
        Macro::togglePlaying();
        Macro::resetState(true);
        return;
      }
    }
  }

  // Frame fixes eliminados para máximo rendimiento
  void applyFrameFixes(int frame) {
    // Sin frame fixes - solo replay básico
    return;
  }

  void handleButton(bool hold, int button, bool player2) {
    auto& g = Global::get();

    if (g.p2mirror && m_gameState.m_isDualMode && !g.autoclicker) {
      GJBaseGameLayer::handleButton(g.mod->getSavedValue<bool>("p2_input_mirror_inverted") ? !hold : hold, button, !player2);
    }

    if (g.state == state::none)
      return GJBaseGameLayer::handleButton(hold, button, player2);

    if (g.state == state::playing) {
      // Permitir clicks del usuario durante reproducción con prioridad sobre el macro
      m_fields->macroInput = false;
      m_fields->userInputActive = true;
      m_fields->userInputFrame = Global::getCurrentFrame();
      m_fields->userInputButton = button;
      m_fields->userInputPlayer2 = player2;
      return GJBaseGameLayer::handleButton(hold, button, player2);
    }
    else if (g.ignoreFrame != -1 && hold)
      return;

    int frame = Global::getCurrentFrame();

    if (frame >= 10 && hold)
      Global::hasIncompatibleMods();

    if (g.state != state::recording) return GJBaseGameLayer::handleButton(hold, button, player2);

    // Sistema básico - solo grabar y aplicar clicks directamente
    GJBaseGameLayer::handleButton(hold, button, player2);

    if (!m_levelSettings->m_twoPlayerMode)
      player2 = false;

    // Grabar el click sin verificaciones complejas
    if (!g.ignoreRecordAction && !g.creatingTrajectory && !m_player1->m_isDead) {
      g.macro.recordAction(frame, button, player2, hold);
      if (g.p2mirror && m_gameState.m_isDualMode)
        g.macro.recordAction(frame, button, !player2, g.mod->getSavedValue<bool>("p2_input_mirror_inverted") ? !hold : hold);
    }
  }
};

class $modify(PauseLayer) {

  void onPracticeMode(CCObject * sender) {
    PauseLayer::onPracticeMode(sender);
    auto& g = Global::get();

    if (g.state != state::none) PlayLayer::get()->resetLevel();

    // Activar automáticamente la grabación cuando se entra en modo práctica
    if (g.state != state::recording) {
      g.state = state::recording;
      g.currentAction = 0;
      g.currentFrameFix = 0;
      g.restart = true;

      // Actualizar la interfaz
      Interface::updateLabels();
      Interface::updateButtons();
      Macro::updateTPS();

      g.lastAutoSaveMS = std::chrono::steady_clock::now();
    }
  }

  void onNormalMode(CCObject * sender) {
    PauseLayer::onNormalMode(sender);
    auto& g = Global::get();

    g.checkpoints.clear();

    // Detener la grabación cuando se sale del modo práctica
    if (g.state == state::recording) {
      g.state = state::none;

      // Actualizar la interfaz
      Interface::updateLabels();
      Interface::updateButtons();
      Macro::updateTPS();
    }

    if (g.restart) {
      if (PlayLayer* pl = PlayLayer::get())
        pl->resetLevel();
    }

  }

  void onQuit(CCObject * sender) {
    PauseLayer::onQuit(sender);

    Macro::resetState();

    Loader::get()->queueInMainThread([] {
      auto& g = Global::get();
      if (g.renderer.recording) g.renderer.stop();
      if (g.renderer.recordingAudio) g.renderer.stopAudio();
    });
  }

  void goEdit() {
    PauseLayer::goEdit();

    Macro::resetState();
    
    Loader::get()->queueInMainThread([] {
      auto& g = Global::get();
      if (g.renderer.recording) g.renderer.stop();
      if (g.renderer.recordingAudio) g.renderer.stopAudio();
    });
  }

};
