src2/
build/
build-android32/
build-android64/

test.txt
test.cpp

# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Macos be like
**/.DS_Store

# Cache files for Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache

# Ignore build folders
**/build
# Ignore platform specific build folders
build-*/

# Workspace files are user-specific
*.sublime-workspace

# ILY vscode
**/.vscode
.idea/

# clangd
.cache/

# Visual Studio
.vs/

# CLion
/cmake-build-*/
